#Wed Jul 30 04:46:40 CST 2025
com.ttv.demo.app-main-44\:/drawable/home_feed_tab_indicator.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_home_feed_tab_indicator.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_assets.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_assets.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_home.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_market.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_market.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_profile.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profile.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_trade.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_trade.xml.flat
com.ttv.demo.app-main-44\:/drawable/ic_wallet.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wallet.xml.flat
com.ttv.demo.app-main-44\:/drawable/item_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_background.xml.flat
com.ttv.demo.app-main-44\:/drawable/release_button_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_release_button_background.xml.flat
com.ttv.demo.app-main-44\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.ttv.demo.app-main-44\:/layout/activity_main_with_navigation.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main_with_navigation.xml.flat
com.ttv.demo.app-main-44\:/layout/content_card.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_content_card.xml.flat
com.ttv.demo.app-main-44\:/layout/floating_tab_layout.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_tab_layout.xml.flat
com.ttv.demo.app-main-44\:/layout/fragment_content.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_content.xml.flat
com.ttv.demo.app-main-44\:/layout/fragment_home_demo.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home_demo.xml.flat
com.ttv.demo.app-main-44\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.ttv.demo.app-main-44\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.ttv.demo.app-main-44\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-44\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-44\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-44\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-44\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-44\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.ttv.demo.app-main-44\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.ttv.demo.app-main-46\:/drawable/home_feed_tab_indicator.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_home_feed_tab_indicator.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_assets.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_assets.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_home.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_market.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_market.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_profile.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profile.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_trade.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_trade.xml.flat
com.ttv.demo.app-main-46\:/drawable/ic_wallet.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_wallet.xml.flat
com.ttv.demo.app-main-46\:/drawable/item_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_item_background.xml.flat
com.ttv.demo.app-main-46\:/drawable/release_button_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_release_button_background.xml.flat
com.ttv.demo.app-main-46\:/font/harmonyos_sans_medium.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_harmonyos_sans_medium.xml.flat
com.ttv.demo.app-main-46\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.ttv.demo.app-main-46\:/layout/activity_main_with_navigation.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main_with_navigation.xml.flat
com.ttv.demo.app-main-46\:/layout/content_card.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_content_card.xml.flat
com.ttv.demo.app-main-46\:/layout/floating_tab_layout.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_tab_layout.xml.flat
com.ttv.demo.app-main-46\:/layout/fragment_content.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_content.xml.flat
com.ttv.demo.app-main-46\:/layout/fragment_home_demo.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home_demo.xml.flat
com.ttv.demo.app-main-46\:/layout/pager_navigator_layout.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pager_navigator_layout.xml.flat
com.ttv.demo.app-main-46\:/layout/pager_navigator_layout_no_scroll.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_pager_navigator_layout_no_scroll.xml.flat
com.ttv.demo.app-main-46\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.ttv.demo.app-main-46\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.ttv.demo.app-main-46\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-46\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-46\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-46\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.ttv.demo.app-main-46\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.ttv.demo.app-main-46\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.ttv.demo.app-main-46\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
