package com.ttv.demo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator

/**
 * Tab双向粘性效果演示Activity
 *
 * 功能说明：
 * 1. 吸顶效果：通过app:layout_isSticky="true"和AppBarLayoutBehavior实现原生吸顶
 * 2. 吸底效果：当Tab滚出屏幕底部时，在屏幕底部显示自定义吸底Tab
 * 3. 协调机制：确保吸顶和吸底效果不会冲突
 * 4. 点击交互：点击吸底Tab时滚动到合适位置显示原始Tab
 */
class StickyBottomTabDemoActivity : AppCompatActivity() {

    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2
    private lateinit var nestedScrollView: NestedScrollView
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var fluentContainer: LinearLayout
    private lateinit var tabLayoutContainer: LinearLayout

    private val tabTitles = listOf("推荐", "关注", "热门", "视频", "直播", "科技", "娱乐", "体育")

    // 标记是否正在初始化，避免启动时自动滑动
    private var isInitializing = true

    // 标记是否正在播放动画，避免动画冲突
    private var isAnimating = false

    // 吸底Tab相关状态
    private var isBottomTabVisible = false
    private var stickyBottomTabContainer: LinearLayout? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        setupContent()
        setupViewPager()
        setupTabLayout()
        setupStickyTabBehavior()
    }

    private fun initViews() {
        coIndicator = findViewById(R.id.coIndicator)
        viewPager = findViewById(R.id.home_viewPager)
        nestedScrollView = findViewById(R.id.fluent_content_nsv)
        appBarLayout = findViewById(R.id.appBarLayout)
        coordinatorLayout = findViewById(R.id.clLayout)
        fluentContainer = findViewById(R.id.fluent_container)
        tabLayoutContainer = findViewById(R.id.home_feed_linear_tabLayout)
    }

    private fun setupContent() {
        // 添加足够多的内容来演示滚动效果，确保NestedScrollView可以滚动
        for (i in 1..10) {
            val cardView = LayoutInflater.from(this).inflate(R.layout.content_card, null)
            val titleText = cardView.findViewById<TextView>(R.id.tv_card_title)
            val contentText = cardView.findViewById<TextView>(R.id.tv_card_content)

            titleText.text = "演示内容 $i"
            contentText.text = "这是第 $i 个内容卡片。向上滑动可以看到Tab吸顶效果，向下滑动可以看到Tab吸底效果。当Tab即将滚出屏幕时，它会自动在对应位置显示粘性Tab保持可见。"

            fluentContainer.addView(cardView)
        }
    }

    private fun setupViewPager() {
        val adapter = TabPagerAdapter(this, tabTitles)
        viewPager.adapter = adapter
        viewPager.offscreenPageLimit = 1
    }

    private fun setupTabLayout() {
        // 为CoIndicator添加Tab
        tabTitles.forEach { title ->
            coIndicator.addTab(title)
        }

        // 设置ViewPager2与CoIndicator的联动
        TabLayoutMediator(coIndicator, viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()
    }

    private fun setupStickyTabBehavior() {
        // 页面初始化完成后设置监听
        coordinatorLayout.post {
            isInitializing = false
            android.util.Log.d("StickyTab", "初始化完成，开始监听滚动")
        }

        // 监听AppBarLayout的滚动变化，主要处理吸底Tab效果
        // 吸顶效果由AppBarLayoutBehavior和app:layout_isSticky="true"处理
        appBarLayout.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            if (!isInitializing && !isAnimating) {
                checkBottomStickyTabVisibility(verticalOffset)
            }
        }
    }

    /**
     * 检测并更新吸底Tab的显示状态
     * 只处理吸底效果，吸顶效果由原生机制处理
     */
    private fun checkBottomStickyTabVisibility(verticalOffset: Int) {
        // 获取Tab的屏幕位置信息
        val location = IntArray(2)
        tabLayoutContainer.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = tabLayoutContainer.height
        val screenHeight = resources.displayMetrics.heightPixels
        val tabBottomY = tabScreenY + tabHeight

        // 判断Tab是否滚出屏幕底部
        val isTabScrolledOutOfBottom = tabScreenY >= screenHeight
        val isTabVisible = tabScreenY < screenHeight && tabBottomY > 0

        android.util.Log.d("StickyTab",
            "吸底检测: verticalOffset=$verticalOffset, " +
            "tabScreenY=$tabScreenY, tabBottomY=$tabBottomY, " +
            "screenHeight=$screenHeight, " +
            "isTabVisible=$isTabVisible, " +
            "isTabScrolledOutOfBottom=$isTabScrolledOutOfBottom")

        when {
            isTabScrolledOutOfBottom && !isBottomTabVisible -> {
                // Tab滚出屏幕底部，显示吸底Tab
                android.util.Log.d("StickyTab", "Tab滚出屏幕底部，显示吸底Tab")
                showBottomStickyTab()
            }
            isTabVisible && isBottomTabVisible -> {
                // Tab重新可见，隐藏吸底Tab
                android.util.Log.d("StickyTab", "Tab重新可见，隐藏吸底Tab")
                hideBottomStickyTab()
            }
        }
    }



    /**
     * 显示吸底Tab
     */
    private fun showBottomStickyTab() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null) return

        android.util.Log.d("StickyTab", "开始创建吸底Tab")
        isAnimating = true
        isBottomTabVisible = true

        // 创建吸底Tab容器
        stickyBottomTabContainer = createStickyTabContainer()
        val stickyIndicator = createStickyIndicator { position ->
            performSmoothTabSwitchFromBottom(position)
        }
        stickyBottomTabContainer?.addView(stickyIndicator)

        // 添加到CoordinatorLayout底部
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = android.view.Gravity.BOTTOM
        }

        coordinatorLayout.addView(stickyBottomTabContainer, layoutParams)

        // 入场动画：从底部滑入
        stickyBottomTabContainer?.let { container ->
            container.translationY = container.height.toFloat()
            container.animate()
                .translationY(0f)
                .setDuration(300)
                .setListener(object : android.animation.AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: android.animation.Animator) {
                        isAnimating = false
                        android.util.Log.d("StickyTab", "吸底Tab入场动画完成")
                    }
                    override fun onAnimationCancel(animation: android.animation.Animator) {
                        isAnimating = false
                    }
                })
                .start()
        }

        // 同步Tab状态
        setupBottomTabSync(stickyIndicator)
    }



    /**
     * 隐藏吸底Tab
     */
    private fun hideBottomStickyTab() {
        if (isAnimating || !isBottomTabVisible || stickyBottomTabContainer == null) return

        android.util.Log.d("StickyTab", "开始隐藏吸底Tab")
        isAnimating = true
        isBottomTabVisible = false

        val containerToRemove = stickyBottomTabContainer
        stickyBottomTabContainer = null

        // 退场动画：向底部滑出
        containerToRemove?.animate()
            ?.translationY(containerToRemove.height.toFloat())
            ?.setDuration(300)
            ?.setListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    try {
                        coordinatorLayout.removeView(containerToRemove)
                        android.util.Log.d("StickyTab", "吸底Tab退场动画完成")
                    } catch (e: Exception) {
                        android.util.Log.e("StickyTab", "移除吸底Tab时发生异常", e)
                    }
                    isAnimating = false
                }
                override fun onAnimationCancel(animation: android.animation.Animator) {
                    isAnimating = false
                }
            })
            ?.start()
    }

    /**
     * 创建粘性Tab容器的通用方法
     */
    private fun createStickyTabContainer(): LinearLayout {
        return LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER_VERTICAL
            setBackgroundColor(0xFFFFFFFF.toInt())
            elevation = 8f
        }
    }

    /**
     * 创建粘性Tab指示器的通用方法
     */
    private fun createStickyIndicator(onTabClick: (position: Int) -> Unit): CoIndicator {
        val stickyIndicator = CoIndicator(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                resources.getDimensionPixelSize(R.dimen.dimen_40),
                1f
            )
            setPadding(resources.getDimensionPixelSize(R.dimen.dimen_8), 0, 0, 0)
        }

        // 复制Tab内容和状态
        tabTitles.forEachIndexed { index, title ->
            stickyIndicator.addTab(title)
        }

        // 同步当前选中的Tab
        stickyIndicator.getTabAt(viewPager.currentItem)?.select()

        // 设置点击监听
        stickyIndicator.setOnTabSelectedListener { position ->
            onTabClick(position)
        }

        return stickyIndicator
    }



    /**
     * 从吸底Tab执行丝滑的Tab切换动画
     * 动画流程：吸底Tab下滑隐藏 → 切换内容 → 滚动让原Tab可见
     */
    private fun performSmoothTabSwitchFromBottom(position: Int) {
        if (isInitializing) {
            android.util.Log.d("StickyTab", "初始化期间，跳过Tab切换动画")
            return
        }

        val container = stickyBottomTabContainer ?: return

        // 第一步：吸底Tab向下滑动隐藏
        container.animate()
            .translationY(container.height.toFloat())
            .setDuration(250)
            .setInterpolator(android.view.animation.DecelerateInterpolator(1.5f))
            .withEndAction {
                // 第二步：切换ViewPager内容
                viewPager.setCurrentItem(position, true)

                // 第三步：滚动到让原Tab显示在屏幕1/3位置
                coordinatorLayout.postDelayed({
                    scrollToShowOriginalTabAtOneThird()
                }, 100)
            }
            .start()
    }



    /**
     * 滚动让原Tab显示在屏幕1/3位置（用于吸底Tab点击后）
     */
    private fun scrollToShowOriginalTabAtOneThird() {
        if (isInitializing) {
            android.util.Log.d("StickyTab", "初始化期间，跳过滚动操作")
            return
        }

        coordinatorLayout.post {
            val screenHeight = resources.displayMetrics.heightPixels
            val targetY = screenHeight / 3 // 目标位置：屏幕1/3处

            // 先完全展开AppBarLayout
            appBarLayout.setExpanded(true, false)

            // 等待布局完成后计算需要的滚动距离
            coordinatorLayout.post {
                val tabLocation = IntArray(2)
                tabLayoutContainer.getLocationOnScreen(tabLocation)
                val currentTabY = tabLocation[1]

                // 计算需要向下滚动的距离
                val scrollDistance = currentTabY - targetY

                if (scrollDistance > 0) {
                    // 使用AppBarLayout的Behavior来精确控制滚动
                    val behavior = (appBarLayout.layoutParams as CoordinatorLayout.LayoutParams).behavior
                    if (behavior is AppBarLayout.Behavior) {
                        val totalScrollRange = appBarLayout.totalScrollRange
                        val targetOffset = Math.min(totalScrollRange, scrollDistance)

                        // 使用动画平滑滚动到目标位置
                        val animator = android.animation.ValueAnimator.ofInt(0, targetOffset)
                        animator.duration = 400
                        animator.interpolator = android.view.animation.DecelerateInterpolator()
                        animator.addUpdateListener { animation ->
                            val offset = animation.animatedValue as Int
                            behavior.topAndBottomOffset = -offset
                            appBarLayout.requestLayout()
                        }
                        animator.start()
                    }
                }

                android.util.Log.d("StickyTab", "滚动到1/3位置完成")
            }
        }
    }

    /**
     * 设置吸底Tab同步逻辑
     */
    private fun setupBottomTabSync(stickyIndicator: CoIndicator) {
        // 使用ViewPager的页面变化监听来同步Tab状态
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 同步吸底Tab的选中状态
                if (isBottomTabVisible) {
                    stickyIndicator.getTabAt(position)?.select()
                }
            }
        })
    }

    /**
     * ViewPager适配器
     */
    private class TabPagerAdapter(
        fragmentActivity: FragmentActivity,
        private val tabTitles: List<String>
    ) : FragmentStateAdapter(fragmentActivity) {

        override fun getItemCount(): Int = tabTitles.size

        override fun createFragment(position: Int): Fragment {
            return ContentFragment.newInstance(tabTitles[position], position)
        }
    }
}