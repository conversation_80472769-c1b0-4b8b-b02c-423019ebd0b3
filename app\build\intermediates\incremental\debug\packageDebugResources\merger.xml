<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\src\main\res"><file name="home_feed_tab_indicator" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\home_feed_tab_indicator.xml" qualifiers="" type="drawable"/><file name="ic_assets" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_assets.xml" qualifiers="" type="drawable"/><file name="ic_home" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_market" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_market.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_trade" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_trade.xml" qualifiers="" type="drawable"/><file name="ic_wallet" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\ic_wallet.xml" qualifiers="" type="drawable"/><file name="item_background" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\item_background.xml" qualifiers="" type="drawable"/><file name="release_button_background" path="D:\AndroidStudioProjects\demo\app\src\main\res\drawable\release_button_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\AndroidStudioProjects\demo\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_content" path="D:\AndroidStudioProjects\demo\app\src\main\res\layout\fragment_content.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\demo\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="color|reference" name="Background_L1"/><attr format="color|reference" name="Button_Blue_Fill"/><attr format="color|reference" name="Text_L3"/><attr format="color|reference" name="Divider_L1"/><declare-styleable name="RoundLinearLayout">
        <attr format="color|reference" name="rv_backgroundColor"/>
        <attr format="dimension" name="rv_cornerRadius"/>
        <attr format="boolean" name="rv_isRippleEnable"/>
    </declare-styleable><attr format="color|reference" name="Functional_Blue"/><attr format="color|reference" name="Text_L1"/></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="blue_primary">#FF1976D2</color><color name="background_color">#FFF5F5F5</color><color name="status_bar_color">#FF6200EE</color><color name="header_background">#FFE3F2FD</color><color name="content_background">#FFEEEEEE</color><color name="fragment_background">#FFFFFFFF</color><color name="tab_background">#FFFFFFFF</color><color name="text_primary">#FF212121</color><color name="text_secondary">#FF757575</color><color name="tab_indicator_color">#FF6200EE</color><color name="tab_selected_text_color">#FF6200EE</color><color name="tab_text_color">#FF757575</color><color name="release_button_text_color">#FF6200EE</color><color name="release_button_background">#FFE8F5E8</color><color name="transparent">#00000000</color><color name="background_l1">#FFF5F5F5</color><color name="button_blue_fill">#FF6200EE</color><color name="text_l3">#FF757575</color><color name="divider_l1">#FFE0E0E0</color><color name="otc_fitter_item_unselect_color">#FFE8F5E8</color></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="dimen_25">25dp</dimen><dimen name="dimen_12">12dp</dimen><dimen name="dimen_40">40dp</dimen><dimen name="dimen_8">8dp</dimen><dimen name="dimen_200">200dp</dimen><dimen name="dimen_20">20dp</dimen><dimen name="dimen_16">16dp</dimen><dimen name="dimen_10">10dp</dimen><dimen name="global_corner_radius_2">8dp</dimen><dimen name="global_text_size_12">12sp</dimen><dimen name="dimen_48">48dp</dimen><dimen name="dimen_1">1dp</dimen></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">demo</string><string name="n_send_comment">发布</string></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\styles.xml" qualifiers=""><style name="FeedTabLayoutStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#666666</item>
    </style><style name="TabTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?attr/Text_L3</item>
    </style></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Demo" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        

        
        <item name="Background_L1">@color/background_l1</item>
        <item name="Button_Blue_Fill">@color/button_blue_fill</item>
        <item name="Text_L1">@color/text_primary</item>
        <item name="Text_L3">@color/text_l3</item>
        <item name="Divider_L1">@color/divider_l1</item>
        <item name="Functional_Blue">@color/button_blue_fill</item>
    </style></file><file path="D:\AndroidStudioProjects\demo\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Demo" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="D:\AndroidStudioProjects\demo\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\AndroidStudioProjects\demo\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\demo\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="RoundLinearLayout">
        <attr format="color|reference" name="rv_backgroundColor"/>
        <attr format="dimension" name="rv_cornerRadius"/>
        <attr format="boolean" name="rv_isRippleEnable"/>
    </declare-styleable></configuration></mergedItems></merger>