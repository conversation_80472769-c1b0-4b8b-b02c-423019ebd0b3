R_DEF: Internal format may change without notice
local
attr Background_L1
attr Button_Blue_Fill
attr Divider_L1
attr Functional_Blue
attr Text_L1
attr Text_L3
attr? rv_backgroundColor
attr? rv_cornerRadius
attr? rv_isRippleEnable
color background_color
color background_l1
color black
color blue_primary
color button_blue_fill
color content_background
color divider_l1
color fragment_background
color header_background
color otc_fitter_item_unselect_color
color purple_200
color purple_500
color purple_700
color release_button_background
color release_button_text_color
color status_bar_color
color tab_background
color tab_indicator_color
color tab_selected_text_color
color tab_text_color
color teal_200
color teal_700
color text_l3
color text_primary
color text_secondary
color transparent
color white
dimen dimen_1
dimen dimen_10
dimen dimen_12
dimen dimen_16
dimen dimen_20
dimen dimen_200
dimen dimen_25
dimen dimen_40
dimen dimen_48
dimen dimen_8
dimen global_corner_radius_2
dimen global_text_size_12
drawable home_feed_tab_indicator
drawable ic_assets
drawable ic_home
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_market
drawable ic_profile
drawable ic_trade
drawable ic_wallet
drawable item_background
drawable release_button_background
id appBarLayout
id clLayout
id coIndicator
id fluent_container
id fluent_content_nsv
id fluent_refresh_layout
id home_feed_linear_tabLayout
id home_navigation_ll
id home_release_btn
id home_status_bar
id home_viewPager
id main_content
id navigation_container
id rl_new_hand_area_animation_layer
id tv_tab_title
id webview_pool_container
layout activity_main
layout fragment_content
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string n_send_comment
style FeedTabLayoutStyle
style TabTextStyle
style Theme.Demo
styleable RoundLinearLayout rv_backgroundColor rv_cornerRadius rv_isRippleEnable
xml backup_rules
xml data_extraction_rules
